// frontend/src/_Dispatchers/SymbolSelectDispatcher.ts
import { EventBus } from '@/events/eventBus';
import { SymbolSelectEvents, MarketEvents } from '@/events/events';
import { getToken } from '@/utils/auth';
import axios from 'axios';
import { io, Socket } from 'socket.io-client';
import { ChartEvents } from '../events/events';
import poolDispatcher from '../_Dispatchers/poolDispatcher'; // 修复导入路径
import { poolCache } from '@/utils/PoolCache';
import { ShapeSignal } from '@/_Modules/Signal/signals/ShapeSignal';
import { Symbol } from '@/shared_types/market'; // 导入Symbol类型

// 定义PoolItem类型
interface PoolItem {
  symbol: string;
  name: string;
  data?: any;
}

/**
 * 选股专用分发器
 * 负责处理前端选股事件、后端调用和进度反馈
 */
class SymbolSelectDispatcher {
  private isInitialized = false;
  private socket: Socket | null = null;
  private isConnected = false;
  private currentTaskId: string | null = null;

  /**
   * 初始化选股分发器（不再自动连接）
   */
  initialize() {
    if (this.isInitialized) {
      console.log('[选股分发器] 已经初始化过');
      return;
    }

    console.log('[选股分发器] 开始初始化');
    
    // 订阅选股相关事件
    this.subscribeToStockSelection();
        
    this.isInitialized = true;
    console.log('[选股分发器] 初始化完成');
  }

  /**
   * 建立Socket.IO连接
   */
  private async establishSocketConnection(): Promise<void> {
    return new Promise((resolve, reject) => {
      // 如果已经连接，则直接返回
      if (this.socket && this.isConnected) {
        resolve();
        return;
      }

      // 如果存在旧的socket实例，先断开
      if (this.socket) {
        this.socket.disconnect();
        this.socket = null;
      }

      // 动态获取WebSocket端口
      this.getWebSocketPort().then(port => {
        if (!port) {
          reject(new Error('无法获取WebSocket端口'));
          return;
        }

        const host = window.location.hostname;
        console.log(`[选股分发器] 连接到Socket.IO服务器: http://${host}:${port}/symbol_select`);

        this.socket = io(`http://${host}:${port}/symbol_select`, {
          path: '/socket.io',
          reconnection: true,
          reconnectionDelay: 1000,
          reconnectionAttempts: 5,
          transports: ['polling', 'websocket'],
          forceNew: true, // 强制创建新连接
          multiplex: false // 禁用多路复用，避免与其他Socket.IO连接冲突
        });
        
        // 连接事件
        this.socket.on('connect', () => {
          console.log('[选股分发器] Socket.IO连接成功，Socket ID:', this.socket?.id);
          console.log('[选股分发器] 连接的命名空间: /symbol_select');
          this.isConnected = true;
          resolve();
        });
        
        // 连接错误事件
        this.socket.on('connect_error', (error) => {
          console.error('[选股分发器] Socket.IO连接错误:', error);
          this.isConnected = false;
          reject(error);
        });
        
        // 断开连接事件
        this.socket.on('disconnect', (reason) => {
          console.log('[选股分发器] Socket.IO连接断开:', reason);
          this.isConnected = false;
        });
        
        // 进度更新事件
        this.socket.on('progress_update', (progressData: SymbolSelectEvents.StockSelectionProgress) => {
          console.log('[选股分发器] [DEBUG] 收到Socket.IO进度更新:', progressData);
          console.log('[选股分发器] [DEBUG] 进度数据详情:', {
            taskId: progressData.taskId,
            stage: progressData.stage,
            current: progressData.current,
            total: progressData.total,
            selectedCount: progressData.selectedCount,
            progress: progressData.progress
          });

          // 转发进度事件到前端组件
          EventBus.emit(SymbolSelectEvents.Types.STOCK_SELECTION_PROGRESS, progressData);

          // 添加调试日志
          console.log('[选股分发器] [DEBUG] 已转发进度事件到前端组件，事件类型:', SymbolSelectEvents.Types.STOCK_SELECTION_PROGRESS);
        });
        
        // 选股完成事件
        this.socket.on('complete_update', (completeData: any) => {
          console.log('[选股分发器] [DEBUG] 收到Socket.IO选股完成通知:', completeData);
          console.log('[选股分发器] [DEBUG] 完成数据详情:', {
            taskId: completeData.taskId,
            action: completeData.action,
            selectedSymbols: completeData.selectedSymbols,
            totalCount: completeData.selectedSymbols?.length || 0
          });

          // 保存选股结果到股票池（无论是完成还是中止）
          if (completeData.selectedSymbols && completeData.selectedSymbols.length > 0) {
            this.saveSelectionResultsToPool(completeData.selectedSymbols);
          }

          // 根据action判断是完成还是中止
          const action = completeData.action || 'completed';
          
          if (action === 'stopped') {
            // 发送选股中止事件到前端组件
            EventBus.emit(SymbolSelectEvents.Types.STOCK_SELECTION_STOPPED, {
              taskId: completeData.taskId,
              message: '选股任务已中止',
              selectedSymbols: completeData.selectedSymbols,
              totalSymbols: completeData.totalSymbols || 0,
              processedSymbols: completeData.processedSymbols || 0,
              selectedCount: completeData.selectedSymbols?.length || 0,
              executionTime: completeData.executionTime || 0
            });
            
            console.log('[选股分发器] 选股被中止，但仍有选中品种，显示结果列表');
          } else {
            // 发送选股完成事件到前端组件
            EventBus.emit(SymbolSelectEvents.Types.STOCK_SELECTION_COMPLETED, {
              taskId: completeData.taskId,
              selectedSymbols: completeData.selectedSymbols,
              totalSymbols: completeData.totalSymbols || 0,
              processedSymbols: completeData.processedSymbols || 0,
              selectedCount: completeData.selectedSymbols?.length || 0,
              executionTime: completeData.executionTime || 0
            });
            
            console.log('[选股分发器] 选股完成，显示结果列表');
          }

          // 如果有选中品种，显示右侧列表并切换到选股结果列表
          if (completeData.selectedSymbols && completeData.selectedSymbols.length > 0) {
            console.log('[选股分发器] 有选中品种，显示右侧列表并切换到选股结果列表');
            
            // 1. 显示右侧列表（ToolPane）
            EventBus.emit(ChartEvents.Types.TOGGLE_SYMBOLLIST, {
              isMobile: false,
              visible: true
            });
            
            // 2. 切换到品种列表面板
            EventBus.emit(MarketEvents.Types.SYMBOLLIST_CHANGED, {
              symbolListName: '选股结果',
              description: '最新选股结果',
              theList: completeData.selectedSymbols || [],
              isSystemList: false,
              tag: 1 // 发送给SymbolList组件
            });
          }

          console.log('[选股分发器] [DEBUG] 已转发完成事件到前端组件，事件类型:', action === 'stopped' ? 'STOCK_SELECTION_STOPPED' : 'STOCK_SELECTION_COMPLETED');
        });
        
        // 订阅确认事件
        this.socket.on('subscribe_confirmed', (data) => {
          console.log('[选股分发器] 订阅确认:', data);
        });
        
        // 取消订阅确认事件
        this.socket.on('unsubscribe_confirmed', (data) => {
          console.log('[选股分发器] 取消订阅确认:', data);
        });
        
        // 错误事件
        this.socket.on('error', (error: any) => {
          console.error('[选股分发器] Socket.IO错误:', error);
        });
      }).catch(error => {
        console.error('[选股分发器] 获取WebSocket端口失败:', error);
        reject(error);
      });
    });
  }

  /**
   * 从端口池获取可用的WebSocket端口
   * @returns {Promise<number|null>} 返回可用的端口号，如果获取失败则返回null
   */
  private async getWebSocketPort(): Promise<number | null> {
    try {
      console.log('[选股分发器] 正在从端口池获取WebSocket端口...');

      const token = getToken();
      if (!token) {
        console.error('[选股分发器] 获取WebSocket端口失败: 未找到认证token');
        throw new Error('获取WebSocket端口失败: 未找到认证token');
      }

      const response = await axios.post('/api/socket/get_port', {}, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.data.success && response.data.port) {
        const port = response.data.port;
        console.log(`[选股分发器] 成功获取WebSocket端口: ${port}`);
        return port;
      } else {
        console.error('[选股分发器] 获取WebSocket端口失败:', response.data.message || '未知错误');
        throw new Error(`获取WebSocket端口失败: ${response.data.message || '未知错误'}`);
      }
    } catch (error) {
      console.error('[选股分发器] 获取WebSocket端口出错:', error);
      throw error;
    }
  }

  /**
   * 断开Socket.IO连接
   */
  private disconnectSocket() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
      console.log('[选股分发器] Socket.IO连接已断开');
    }
  }

  /**
   * 生成任务ID
   */
  private generateTaskId(): string {
    return 'task_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }
  
  /**
   * 订阅任务进度
   */
  private subscribeToTask(taskId: string) {
    if (!this.isConnected || !this.socket) {
      console.warn('[选股分发器] 无法订阅任务进度，Socket.IO未连接');
      return;
    }

    console.log(`[选股分发器] 订阅任务进度`);
    console.log(`[选股分发器] Socket连接状态: ${this.socket.connected ? '已连接' : '未连接'}`);
    console.log(`[选股分发器] Socket ID: ${this.socket.id}`);
    
    // 发送 subscribe_task 事件
    this.socket.emit('subscribe_task', {});
    this.currentTaskId = taskId;

    console.log(`[选股分发器] 已发送 subscribe_task 请求`);
  }
  
  /**
   * 取消订阅任务进度
   */
  private unsubscribeFromTask(taskId: string) {
    if (!this.isConnected || !this.socket) {
      return;
    }
    
    console.log(`[选股分发器] 取消订阅任务进度`);
    // 发送 unsubscribe_task 事件
    this.socket.emit('unsubscribe_task', {});
    console.log(`[选股分发器] 已发送 unsubscribe_task 请求`);
    
    if (this.currentTaskId === taskId) {
      this.currentTaskId = null;
    }
  }

  /**
   * 测试事件定义是否正确
   */
  testEventDefinitions() {
    console.log('[选股分发器] 测试事件定义...');
    
    // 测试所有选股事件类型
    const eventTypes = [
      SymbolSelectEvents.Types.START_STOCK_SELECTION,
      SymbolSelectEvents.Types.STOP_STOCK_SELECTION,
      SymbolSelectEvents.Types.STOCK_SELECTION_STARTED,
      SymbolSelectEvents.Types.STOCK_SELECTION_STOPPED,
      SymbolSelectEvents.Types.STOCK_SELECTION_PROGRESS,
      SymbolSelectEvents.Types.STOCK_SELECTION_COMPLETED,
      SymbolSelectEvents.Types.STOCK_SELECTION_ERROR,
      SymbolSelectEvents.Types.STOCK_SELECTION_TEST,
    ];

    eventTypes.forEach(eventType => {
      console.log(`[选股分发器] 事件类型: ${eventType}`);
    });

    console.log('[选股分发器] 事件定义测试完成');
  }

  /**
   * 订阅选股事件
   */
  private subscribeToStockSelection() {
    console.log('[选股分发器] 开始订阅选股事件...');
    
    // 监听前端发出的选股请求 - 绑定this上下文
    EventBus.on(SymbolSelectEvents.Types.START_STOCK_SELECTION, this.handleStartStockSelection.bind(this));
    console.log('[选股分发器] 已订阅 START_STOCK_SELECTION 事件');
    
    // 监听停止选股请求 - 绑定this上下文
    EventBus.on(SymbolSelectEvents.Types.STOP_STOCK_SELECTION, this.handleStopStockSelection.bind(this));
    console.log('[选股分发器] 已订阅 STOP_STOCK_SELECTION 事件');
    
    // 监听选股开始事件，订阅任务进度
    EventBus.on(SymbolSelectEvents.Types.STOCK_SELECTION_STARTED, (payload: SymbolSelectEvents.StockSelectionStartedPayload) => {
      console.log('[选股分发器] 收到选股开始事件，订阅任务进度');
      this.currentTaskId = payload.taskId;

      // 如果Socket.IO已连接，立即订阅任务进度
      if (this.isConnected && this.socket) {
        console.log('[选股分发器] Socket.IO已连接，立即订阅任务进度');
        this.subscribeToTask(payload.taskId);
      } else {
        console.log('[选股分发器] Socket.IO未连接，等待连接后订阅任务进度');
      }
    });

    // 监听选股测试事件
    EventBus.on(SymbolSelectEvents.Types.STOCK_SELECTION_TEST, () => {
      console.log('[选股分发器] 收到选股测试事件，开始调用测试API');

      this.startStockSelectionTest();
    });

    // 监听选股完成事件
    EventBus.on(SymbolSelectEvents.Types.STOCK_SELECTION_COMPLETED, () => {
      console.log('[选股分发器] 收到选股完成事件');
      this.handleStockSelectionComplete();
    });

    // 监听选股错误事件
    EventBus.on(SymbolSelectEvents.Types.STOCK_SELECTION_ERROR, () => {
      console.log('[选股分发器] 收到选股错误事件');
      this.handleStockSelectionError();
    });
    
    console.log('[选股分发器] 选股事件订阅完成');
  }

  private signalToModuleName(signalClassName: string): string {
    switch (signalClassName) {
      case 'ShapeSignal':
        return 'shape_matching';
      default:
        return signalClassName;
    }
  }

  /**
   * 处理开始选股请求
   * @param payload 选股配置参数
   */
  private async handleStartStockSelection(payload: SymbolSelectEvents.StartStockSelectionPayload) {
      try {
        console.log('[选股分发器] 收到选股请求:', payload);
  
        // ****** Now, payload.signalConfig.name == 'ShapeSignal' ******
  
        // 创建信号实例 - 需要添加name属性以符合SignalConfig接口
        const signalConfigWithName = {
          name: payload.signalConfig.signalClassName,  // 使用signalClassName作为name
          parameters: payload.signalConfig.parameters
        };

        let signalInstance: ShapeSignal | null = null;

        if (payload.signalConfig.signalClassName === 'ShapeSignal') {

          signalInstance = new ShapeSignal(signalConfigWithName);

        } else {
          
          throw new Error('目前仅处理形态选股的策略类型，其他全部忽略');
        }
  
        // 等待形态配置加载完成
        let values: number[] = [];
        let retryCount = 0;
        const maxRetries = 10;
        
        while (retryCount < maxRetries) {
          values = signalInstance.getShapeValues();
          if (values && values.length > 0) {
            console.log('[选股分发器] 成功获取形态特征序列:', values);
            break;
          }
          
          console.log(`[选股分发器] 等待形态配置加载... (${retryCount + 1}/${maxRetries})`);
          await new Promise(resolve => setTimeout(resolve, 500)); // 等待500ms
          retryCount++;
        }
        
        if (!values || values.length === 0) {
          throw new Error('无法获取形态特征序列，请检查形态配置是否正确加载');
        }
  
        // 执行启动选股的各项准备工作
        const taskId = await this.prepareStockSelection();
        
        // 4. 调用后端API，传递taskId
        const token = getToken();
  
        // payload.signalCofnig.params 添加一个元素 values 记录该形态的特征序列，从 signalInstance.getShapeValues() 获取
        payload.signalConfig.parameters.values = values;
  
        // 确保threshold参数存在，从matchThreshold映射
        if (payload.signalConfig.parameters.matchThreshold !== undefined) {
          payload.signalConfig.parameters.threshold = payload.signalConfig.parameters.matchThreshold / 100; // 转换为0-1范围
        } else {
          payload.signalConfig.parameters.threshold = 0.8; // 默认阈值
        }
  
        const response = await axios.post('/api/stock-selection/start', {
          taskId: taskId,
          candidate_type: payload.candidateType,
          strategy_module: this.signalToModuleName(payload.signalConfig.signalClassName),
          strategy_parameters: payload.signalConfig.parameters
        }, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
  
        if (response.data.success) {
          console.log('[选股分发器] 选股任务启动成功:', response.data);
          
          // 发送选股开始事件
          EventBus.emit(SymbolSelectEvents.Types.STOCK_SELECTION_STARTED, {
            taskId: taskId,
            message: '选股任务已启动'
          });
        } else {
          throw new Error(response.data.error || '启动选股任务失败');
        }
      } catch (error) {
        console.error('[选股分发器] 启动选股任务失败:', error);
        
        // 发生错误时断开连接
        this.disconnectSocket();
        
        // 发送错误事件
        EventBus.emit(SymbolSelectEvents.Types.STOCK_SELECTION_ERROR, {
          message: error instanceof Error ? error.message : '启动选股任务失败'
        });
      }
    }





  /**
   * 保存选股结果到股票池管理
   * @param symbols 选中的品种列表
   */
  private async saveSelectionResultsToPool(symbols: any[]) {
    console.log('[选股分发器] 开始保存选股结果到股票池');
    
    try {
      // 将后端返回的品种数据转换为Symbol对象
      const symbolObjects: Symbol[] = symbols.map((s: any) => ({
        code: s.code,
        name: s.name || s.code,
        market: s.market || 'STOCK',
        exchange: s.exchange || 'SSE'
      } as Symbol));

      // 直接调用marketDispatcher的addSymbolsToResult方法
      const { default: marketDispatcher } = await import('./marketDispatcher');
      await marketDispatcher.addSymbolsToResult(symbolObjects);
      
      console.log('[选股分发器] 选股结果已保存到股票池');
    } catch (error) {
      console.error('[选股分发器] 保存选股结果到股票池失败:', error);
    }
  }

  /**
   * 启动选股
   */
  private async prepareStockSelection() {
    console.log('[选股分发器] 开始准备选股...');
    let taskId: string = '';

    try {
      // 1. 生成任务ID
      taskId = this.generateTaskId();
      this.currentTaskId = taskId;
      
      // 2. 建立Socket连接
      await this.establishSocketConnection();
      
      // 3. 加入任务房间
      this.subscribeToTask(taskId);
      
    } catch (error) {
      console.error('[选股分发器] 启动选股任务失败:', error);
      
      // 发生错误时断开连接
      this.disconnectSocket();

      // 发送错误事件
      EventBus.emit(SymbolSelectEvents.Types.STOCK_SELECTION_ERROR, {
        message: error instanceof Error ? error.message : '启动选股任务失败'
      });
    }

    return taskId;
  }

  private async startStockSelectionTest() {

    const taskId = await this.prepareStockSelection();

    // 4. 调用后端API，传递taskId
    const response = await fetch('/api/stock-selection/test', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getToken()}`
      },
      body: JSON.stringify({ taskId: taskId })
    });

    const result = await response.json();

    if (result.success) {
      console.log('[选股分发器] 选股测试任务启动成功:', result);

      // 发送选股开始事件
      EventBus.emit(SymbolSelectEvents.Types.STOCK_SELECTION_STARTED, {
        taskId: taskId,
        message: '选股任务已启动'
      });
    } else {
      throw new Error(result.error || '启动选股任务失败');
    }
  }

  /**
   * 处理停止选股请求
   * @param payload 停止选股参数
   */
  private async handleStopStockSelection(payload: SymbolSelectEvents.StopStockSelectionPayload) {
    try {
      console.log('[选股分发器] 收到停止选股请求:', payload);
      console.log('[选股分发器] 当前任务ID:', this.currentTaskId);
      console.log('[选股分发器] 请求中的任务ID:', payload.taskId);
      
      // 使用当前任务ID或请求中的任务ID
      const taskId = payload.taskId || this.currentTaskId || 'unknown';
      console.log('[选股分发器] 最终使用的任务ID:', taskId);
      
      const token = getToken();
      const response = await axios.post('/api/stock-selection/stop', {
        taskId: taskId
      }, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.data.success) {
        console.log('[选股分发器] 选股任务停止成功');
        
        // 发送选股停止事件
        EventBus.emit(SymbolSelectEvents.Types.STOCK_SELECTION_STOPPED, {
          taskId: taskId,
          message: '选股任务已停止'
        });
      } else {
        throw new Error(response.data.error || '停止选股任务失败');
      }
    } catch (error) {
      console.error('[选股分发器] 停止选股任务失败:', error);
      
      // 发送错误事件
      EventBus.emit(SymbolSelectEvents.Types.STOCK_SELECTION_ERROR, {
        message: error instanceof Error ? error.message : '停止选股任务失败'
      });
    }
  }

  /**
   * 获取选股结果
   * @param taskId 任务ID
   */
  async getStockSelectionResult(taskId: string): Promise<SymbolSelectEvents.StockSelectionResult> {
    try {
      const token = getToken();
      const response = await axios.get(`/api/stock-selection/result/${taskId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.data.success) {
        return response.data.data;
      } else {
        throw new Error(response.data.error || '获取选股结果失败');
      }
    } catch (error) {
      console.error('[选股分发器] 获取选股结果失败:', error);
      throw error;
    }
  }

  /**
   * 获取选股任务状态
   * @param taskId 任务ID
   */
  async getStockSelectionStatus(taskId: string): Promise<SymbolSelectEvents.StockSelectionStatus> {
    try {
      const token = getToken();
      const response = await axios.get(`/api/stock-selection/status/${taskId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.data.success) {
        return response.data.data;
      } else {
        throw new Error(response.data.error || '获取选股状态失败');
      }
    } catch (error) {
      console.error('[选股分发器] 获取选股状态失败:', error);
      throw error;
    }
  }

  /**
   * 监听选股结果
   * @param callback 结果回调函数
   */
  onSelectionResult(callback: (result: SymbolSelectEvents.StockSelectionResult) => void) {
    return EventBus.on(SymbolSelectEvents.Types.STOCK_SELECTION_COMPLETED, callback);
  }

  /**
   * 监听选股错误
   * @param callback 错误回调函数
   */
  onSelectionError(callback: (error: SymbolSelectEvents.StockSelectionError) => void) {
    return EventBus.on(SymbolSelectEvents.Types.STOCK_SELECTION_ERROR, callback);
  }

  /**
   * 处理选股完成事件
   */
  private handleStockSelectionComplete() {
    // 清理当前任务ID
    this.currentTaskId = null;
    // 断开Socket连接
    this.disconnectSocket();
  }

  /**
   * 处理选股失败事件
   */
  private handleStockSelectionError() {
    // 清理当前任务ID
    this.currentTaskId = null;
    // 断开Socket连接
    this.disconnectSocket();
  }
}

// 创建单例实例
export const symbolSelectDispatcher = new SymbolSelectDispatcher();

// 导出初始化函数
export function initializeSymbolSelectDispatcher() {
  symbolSelectDispatcher.initialize();
  // 测试事件定义
  symbolSelectDispatcher.testEventDefinitions();
}
